const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const DEFAULT_IMAGE_URL = "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRfPpXoRFZWRR_1kXmd5PgzzVdFFt9FFHNGrw&s";

const listingSchema = new Schema({
    title: {
        type: String,
        default: 'Default PG'
    },
    description: {
        type: String,
        default: 'Default description'
    },
    image: {
        type: String,
        default: DEFAULT_IMAGE_URL,
        set: (v) => {
            return v?.trim() === "" || v == null ? DEFAULT_IMAGE_URL : v;
        }
    },
    price: {
        type: Number,
        default: 3000
    },
    location: {
        type: String,
        default: 'Default location'
    },
    landmark: {
        type: String,
        default: 'Default landmark'
    },
    owner: {
        name: {
            type: String,
            default: 'Default Owner'
        },
        contact: {
            type: String,
            default: '9876543210'
        }
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    // Reference to the user who created this listing
    author: {
        type: Schema.Types.ObjectId,
        ref: 'User'
    }
});

const Listing = mongoose.model("Listing", listingSchema);
module.exports = Listing;
