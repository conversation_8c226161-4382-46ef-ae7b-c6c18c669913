const { listingSchema } = require('./schema.js');
const Listing = require('./models/listing');
const Review = require('./models/review');

module.exports.validateListing = (req, res, next) => {
    const { error } = listingSchema.validate(req.body);
    if (error) {
        const msg = error.details.map(el => el.message).join(',');
        const err = new Error(msg);
        err.status = 400;
        throw err;
    } else {
        next();
    }
};

// Check if user is logged in
module.exports.isLoggedIn = (req, res, next) => {
    if (!req.isAuthenticated()) {
        req.session.returnTo = req.originalUrl;
        req.flash('error', 'You must be signed in to access this page');
        return res.redirect('/login');
    }
    next();
};

// Check if user is the author of a listing
module.exports.isAuthor = async (req, res, next) => {
    const { id } = req.params;
    const listing = await Listing.findById(id);
    if (!listing) {
        req.flash('error', 'Listing not found!');
        return res.redirect('/listings');
    }
    if (!listing.author || !listing.author.equals(req.user._id)) {
        req.flash('error', 'Access Denied! You can only edit/delete your own listings. This listing belongs to another user.');
        return res.redirect(`/listings/${id}`);
    }
    next();
};

// Check if user is an owner (for creating listings)
module.exports.isOwner = (req, res, next) => {
    if (!req.user || req.user.userType !== 'owner') {
        req.flash('error', 'Access Denied! Only PG owners can create new listings. Please register as a PG owner to add listings.');
        return res.redirect('/listings');
    }
    next();
};

// Check if user is the author of a review
module.exports.isReviewAuthor = async (req, res, next) => {
    const { reviewId } = req.params;
    const review = await Review.findById(reviewId);
    if (!review) {
        req.flash('error', 'Review not found!');
        return res.redirect('/listings');
    }
    if (!review.author || !review.author.equals(req.user._id)) {
        req.flash('error', 'Access Denied! You can only delete your own reviews. This review was written by another user.');
        return res.redirect(`/listings/${req.params.id}`);
    }
    next();
};

// Check if user can create reviews (must be logged in and be a student)
module.exports.canCreateReview = (req, res, next) => {
    if (!req.user) {
        req.flash('error', 'You must be logged in to write a review');
        return res.redirect('/login');
    }
    if (req.user.userType !== 'student') {
        req.flash('error', 'Only students can write reviews for PG listings');
        return res.redirect(`/listings/${req.params.id}`);
    }
    next();
};
