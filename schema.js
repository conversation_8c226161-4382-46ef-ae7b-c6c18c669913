const Joi = require('joi');

module.exports.listingSchema = Joi.object({
    title: Joi.string().allow(''),
    description: Joi.string().allow(''),
    price: Joi.number().allow(''),
    location: Joi.string().allow(''),
    landmark: Joi.string().allow(''),
    image: Joi.string().allow(''),
    owner: Joi.object({
        name: Joi.string().allow(''),
        contact: Joi.string().allow('')
    }).allow('')
}).unknown(true);
