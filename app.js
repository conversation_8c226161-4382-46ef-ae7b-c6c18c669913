const express = require('express');
const app = express();
const Listing = require("./models/listing.js");
const mongoose = require('mongoose');
const path = require('path');
const methodOverride = require('method-override');
const ejsMate = require('ejs-mate');
const wrapAsync = require('./utils/wrapAsync');
const ExpressError = require('./utils/ExpressError');
// Removed validation middleware import

const MONGO_URL = "mongodb://127.0.0.1:27017/findmypg";

async function main() {
    await mongoose.connect(MONGO_URL);
}

app.engine('ejs', ejsMate);
app.set('views', path.join(__dirname, 'views'));
app.set('view engine', 'ejs');
app.use(express.static(path.join(__dirname, 'public')));
app.use(express.urlencoded({ extended: true }));
app.use(methodOverride('_method'));

main().then(() => {
    console.log("connected to database");
}).catch((err) => {
    console.log("Problem with connecting to database");
});

// Test route to add sample listing
app.get('/testListing', async (req, res) => {
    try {
        let sampleListing = new Listing({
            title: "Test PG - " + new Date().toISOString(),
            description: "This is a test PG created automatically",
            price: 4500,
            location: "Test City",
            landmark: "Near Test Landmark",
            image: "https://images.unsplash.com/photo-1555854877-bab0e564b8d5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
            owner: {
                name: "Test Owner",
                contact: "9876543210"
            }
        });
        await sampleListing.save();
        console.log("Test listing created successfully:", sampleListing._id);
        res.send(`Test listing created successfully! ID: ${sampleListing._id}. <a href="/listings">View all listings</a>`);
    } catch (error) {
        console.log("Error creating test listing:", error);
        res.send("Error creating test listing: " + error.message);
    }
});

// Simple test POST route
app.post('/testPost', (req, res) => {
    console.log('TEST POST route hit!');
    console.log('Body:', req.body);
    res.json({ message: 'POST route working!', body: req.body });
});

app.get('/', (req, res) => {
    res.send("Server is running");
});

// Index route
app.get("/listings", wrapAsync(async (req, res) => {
    const allListings = await Listing.find({});
    res.render("listings/index", { allListings });
}));

// New route
app.get("/listings/new", (req, res) => {
    res.render("listings/new");
});

// Create route - No validation
app.post("/listings", wrapAsync(async (req, res) => {
    console.log('POST /listings received!');
    console.log('Body:', req.body);

    try {
        // Create listing with default values for missing fields
        const newListing = new Listing({
            title: req.body.title || 'Default PG',
            description: req.body.description || 'Default description',
            price: req.body.price || 3000,
            location: req.body.location || 'Default location',
            landmark: req.body.landmark || 'Default landmark',
            image: req.body.image || 'https://images.unsplash.com/photo-1555854877-bab0e564b8d5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
            owner: {
                name: req.body.ownerName || req.body['owner[name]'] || 'Default Owner',
                contact: req.body.ownerContact || req.body['owner[contact]'] || '9876543210'
            }
        });

        await newListing.save();
        console.log('Listing created successfully:', newListing._id);
        res.redirect("/listings");
    } catch (error) {
        console.log('Error creating listing:', error);
        res.send('Error creating listing: ' + error.message);
    }
}));

// Edit route
app.get("/listings/:id/edit", wrapAsync(async (req, res) => {
    let { id } = req.params;
    const listing = await Listing.findById(id);
    if (!listing) {
        throw new ExpressError("Listing not found", 404);
    }
    res.render("listings/edit", { listing });
}));

// Update route - No validation
app.put("/listings/:id", wrapAsync(async (req, res) => {
    let { id } = req.params;
    await Listing.findByIdAndUpdate(id, { ...req.body });
    res.redirect(`/listings/${id}`);
}));

// Delete route
app.delete("/listings/:id", wrapAsync(async (req, res) => {
    let { id } = req.params;
    await Listing.findByIdAndDelete(id);
    res.redirect("/listings");
}));

// Show route
app.get("/listings/:id", wrapAsync(async (req, res) => {
    let { id } = req.params;
    const listing = await Listing.findById(id);
    if (!listing) {
        throw new ExpressError("Listing not found", 404);
    }
    res.render("listings/show", { listing });
}));

// 404 Error Handler
app.use((req, res, next) => {
    next(new ExpressError("Page Not Found", 404));
});

// Error handling middleware
app.use((err, req, res, next) => {
    let { statusCode = 500, message = "Something went wrong!" } = err;
    res.status(statusCode).render("error", { message });
});

app.listen(8080, () => {
    console.log("Server is listening to port 8080");
});
