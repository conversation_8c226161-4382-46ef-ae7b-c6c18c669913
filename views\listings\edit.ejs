<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PG Dedo - Edit <%= listing.title %></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>
    <%- include('../includes/navbar') %>

    <!-- Main Content -->
    <main>
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="mb-0"><i class="fas fa-edit me-2"></i>Edit PG Listing</h4>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="/listings/<%= listing._id %>?_method=PUT" class="needs-validation" novalidate>
                                <div class="mb-3">
                                    <label for="title" class="form-label">PG Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="title" name="title" required minlength="3" maxlength="100" placeholder="Enter PG name (e.g., Sunrise PG)" value="<%= listing.title %>">
                                    <div class="valid-feedback">
                                        Great! PG name looks good.
                                    </div>
                                    <div class="invalid-feedback">
                                        Please provide a valid PG name (3-100 characters).
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="description" name="description" rows="4" required minlength="10" maxlength="1000" placeholder="Describe the PG facilities, amenities, and features"><%= listing.description %></textarea>
                                    <div class="valid-feedback">
                                        Perfect! Description is detailed and helpful.
                                    </div>
                                    <div class="invalid-feedback">
                                        Please provide a detailed description (10-1000 characters).
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="price" class="form-label">Monthly Rent <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <span class="input-group-text">₹</span>
                                                <input type="number" class="form-control" id="price" name="price" required placeholder="3000" min="1000" max="100000" value="<%= listing.price %>">
                                            </div>
                                            <div class="valid-feedback">
                                                Excellent! Price is within acceptable range.
                                            </div>
                                            <div class="invalid-feedback">
                                                Please enter a valid price between ₹1,000 and ₹1,00,000.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="location" name="location" required minlength="2" maxlength="50" placeholder="Enter city/area (e.g., Mathura)" value="<%= listing.location %>">
                                            <div class="valid-feedback">
                                                Perfect! Location is clear and specific.
                                            </div>
                                            <div class="invalid-feedback">
                                                Please provide a valid location (2-50 characters).
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="landmark" class="form-label">Landmark</label>
                                    <input type="text" class="form-control" id="landmark" name="landmark" maxlength="100" placeholder="Enter nearby landmark (e.g., Near GLA Gate)" value="<%= listing.landmark %>">
                                    <div class="valid-feedback">
                                        Great! Landmark will help students find the PG easily.
                                    </div>
                                    <div class="invalid-feedback">
                                        Landmark should be less than 100 characters.
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="image" class="form-label">Image URL (optional)</label>
                                    <input type="url" class="form-control" id="image" name="image" placeholder="Enter image URL (leave blank for default image)" value="<%= listing.image %>">
                                    <div class="valid-feedback">
                                        Perfect! Valid image URL provided.
                                    </div>
                                    <div class="invalid-feedback">
                                        Please enter a valid URL (e.g., https://example.com/image.jpg).
                                    </div>
                                </div>

                                <% if (listing.owner && listing.owner.name) { %>
                                <div class="mb-3">
                                    <label for="ownerName" class="form-label">Owner Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="ownerName" name="owner[name]" required minlength="2" maxlength="50" placeholder="Enter owner's name" value="<%= listing.owner.name %>">
                                    <div class="valid-feedback">
                                        Excellent! Owner name is clear and professional.
                                    </div>
                                    <div class="invalid-feedback">
                                        Please provide a valid owner name (2-50 characters).
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="ownerContact" class="form-label">Owner Contact <span class="text-danger">*</span></label>
                                    <input type="tel" class="form-control" id="ownerContact" name="owner[contact]" required pattern="^\+91\s?\d{10}$" placeholder="Enter contact number (e.g., +91 9876543210)" value="<%= listing.owner.contact %>">
                                    <div class="valid-feedback">
                                        Perfect! Contact number is in correct format.
                                    </div>
                                    <div class="invalid-feedback">
                                        Please enter a valid Indian mobile number (+91 followed by 10 digits).
                                    </div>
                                </div>
                                <% } else { %>
                                <div class="mb-3">
                                    <label for="ownerName" class="form-label">Owner Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="ownerName" name="owner[name]" required minlength="2" maxlength="50" placeholder="Enter owner's name">
                                    <div class="valid-feedback">
                                        Excellent! Owner name is clear and professional.
                                    </div>
                                    <div class="invalid-feedback">
                                        Please provide a valid owner name (2-50 characters).
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="ownerContact" class="form-label">Owner Contact <span class="text-danger">*</span></label>
                                    <input type="tel" class="form-control" id="ownerContact" name="owner[contact]" required pattern="^\+91\s?\d{10}$" placeholder="Enter contact number (e.g., +91 9876543210)">
                                    <div class="valid-feedback">
                                        Perfect! Contact number is in correct format.
                                    </div>
                                    <div class="invalid-feedback">
                                        Please enter a valid Indian mobile number (+91 followed by 10 digits).
                                    </div>
                                </div>
                                <% } %>

                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-success btn-lg">
                                        <i class="fas fa-save me-2"></i>Update PG Listing
                                    </button>
                                    <a href="/listings/<%= listing._id %>" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Cancel
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <%- include('../includes/footer') %>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Form Validation Script -->
    <script>
        // Bootstrap form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();

        // Sidebar functionality
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');
        const sidebarOverlay = document.getElementById('sidebarOverlay');
        const sidebarClose = document.getElementById('sidebarClose');
        const main = document.querySelector('main');
        let sidebarOpen = true; // Default state is open

        function toggleSidebar() {
            if (sidebarOpen) {
                closeSidebar();
            } else {
                openSidebar();
            }
        }

        function openSidebar() {
            sidebar.classList.remove('hidden');
            main.classList.remove('sidebar-hidden');
            sidebarToggle.innerHTML = '<i class="fas fa-times"></i>';
            sidebarOpen = true;
        }

        function closeSidebar() {
            sidebar.classList.add('hidden');
            main.classList.add('sidebar-hidden');
            sidebarToggle.innerHTML = '<i class="fas fa-bars"></i>';
            sidebarOpen = false;
        }

        // Initialize sidebar state
        sidebarToggle.innerHTML = '<i class="fas fa-times"></i>';

        sidebarToggle.addEventListener('click', toggleSidebar);
        sidebarClose.addEventListener('click', closeSidebar);
        sidebarOverlay.addEventListener('click', closeSidebar);

        // Close sidebar on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && sidebarOpen) {
                closeSidebar();
            }
        });
    </script>
</body>
</html>
