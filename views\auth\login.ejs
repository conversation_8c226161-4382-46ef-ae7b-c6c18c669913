<% layout('layouts/boilerplate') %>

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow-lg" style="background: rgba(26, 26, 26, 0.95); border: none; border-radius: 20px;">
                <div class="card-header text-center" style="background: linear-gradient(135deg, #28a745, #20c997); border-radius: 20px 20px 0 0;">
                    <h3 class="mb-0 text-white">
                        <i class="fas fa-sign-in-alt me-2"></i>Welcome Back
                    </h3>
                </div>
                <div class="card-body p-4">
                    <form action="/login" method="POST" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="username" class="form-label text-white">
                                <i class="fas fa-envelope me-2"></i>Email Address
                            </label>
                            <input type="email" class="form-control" id="username" name="username" required 
                                   placeholder="Enter your email address">
                            <div class="valid-feedback">Looks good!</div>
                            <div class="invalid-feedback">Please provide your email address.</div>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label text-white">
                                <i class="fas fa-lock me-2"></i>Password
                            </label>
                            <input type="password" class="form-control" id="password" name="password" required 
                                   placeholder="Enter your password">
                            <div class="valid-feedback">Looks good!</div>
                            <div class="invalid-feedback">Please provide your password.</div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>Sign In
                            </button>
                        </div>
                    </form>

                    <hr class="my-4" style="border-color: rgba(255, 255, 255, 0.2);">
                    
                    <div class="text-center">
                        <p class="text-white mb-0">Don't have an account?</p>
                        <a href="/register" class="btn btn-outline-light mt-2">
                            <i class="fas fa-user-plus me-2"></i>Create Account
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();
</script>
