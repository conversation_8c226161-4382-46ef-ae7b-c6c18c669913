
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

html, body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(-45deg, #2a2a2a, #473f3f, #191717, #3a3a3a, #050404, #151515) !important;
    background-size: 400% 400% !important;
    animation: gradientShift 12s ease infinite !important;
    color: #ffffff !important;
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

body {
    display: flex;
    flex-direction: column;
    position: relative;
    background-attachment: fixed !important;
}
@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    25% {
        background-position: 100% 50%;
    }
    50% {
        background-position: 100% 100%;
    }
    75% {
        background-position: 0% 100%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Ensure gradient is always visible */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(-45deg, #2a2a2a, #3a3a3a, #4a4a4a, #3a3a3a, #2f2f2f, #454545);
    background-size: 400% 400%;
    animation: gradientShift 12s ease infinite;
    z-index: -2;
    pointer-events: none;
}

/* Additional overlay for content readability */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.1);
    z-index: -1;
    pointer-events: none;
}

/* Sidebar Toggle Button */
.sidebar-toggle {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1040;
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(15px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.2rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.sidebar-toggle:hover {
    background: rgba(13, 110, 253, 0.9);
    border-color: rgba(13, 110, 253, 0.8);
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(13, 110, 253, 0.4);
}

/* Sidebar Overlay */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 1035;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Vertical Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 320px;
    height: 100vh;
    background: rgba(0, 10, 21, 0.722);
    backdrop-filter: blur(20px);
    border-right: 1px solid rgb(255, 255, 255);
    box-shadow: 4px 0 20px rgba(253, 253, 253, 0.314);
    z-index: 1036;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
}

.sidebar.hidden {
    left: -320px;
}

/* Sidebar Header */
.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.916);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 80px;
    position: relative;
    margin-top: 80px;
}

.sidebar-brand {
    display: flex;
    align-items: center;
    color: #ffffff;
    font-weight: 700;
    font-size: 1.5rem;
    text-decoration: none;
    flex: 1;
    padding-right: 50px;
}

.sidebar-brand i {
    color: #007bff;
    margin-right: 10px;
}

.sidebar-close {
    position: absolute;
    top: 20px;
    right: 20px;
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-close:hover {
    color: #ffffff;
    background: rgb(159, 159, 159);
}

/* Sidebar Content */
.sidebar-content {
    flex: 1;
    padding: 40px 0 20px 0;
    overflow-y: auto;
}

.sidebar-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-nav .nav-item {
    margin-bottom: 5px;
}

.sidebar-nav .nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-radius: 0;
    font-weight: 500;
}

.sidebar-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    padding-left: 30px;
}

.sidebar-nav .nav-link i {
    width: 20px;
    margin-right: 15px;
    font-size: 1.1rem;
    color: #007bff;
}

.sidebar-nav .nav-link .nav-text {
    font-size: 1rem;
}

/* Sidebar Divider */
.sidebar-divider {
    height: 1px;
    background: rgba(210, 209, 209, 0.967);
    margin: 20px 20px;
}

/* Sidebar Footer */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgb(255, 255, 255);
}

.sidebar-info {
    display: flex;
    align-items: center;
    color: rgb(255, 255, 255);
    font-size: 0.9rem;
}

.sidebar-info i {
    margin-right: 10px;
    color: #007bff;
}

/* Sidebar User Info */
.sidebar-user-info {
    padding: 1rem;
    margin: 1rem 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.user-avatar {
    text-align: center;
    margin-bottom: 0.5rem;
}

.user-avatar i {
    font-size: 2.5rem;
    color: #007bff;
}

.user-details {
    text-align: center;
}

.user-name {
    color: #ffffff;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0.3rem;
}

.user-type {
    margin-top: 0.3rem;
}

.user-type .badge {
    font-size: 0.75rem;
    padding: 0.3rem 0.6rem;
}

/* Main Content */
main {
    flex: 1;
    padding-top: 2rem;
    padding-bottom: 3rem;
    margin-left: 320px;
    transition: margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

main.sidebar-hidden {
    margin-left: 0;
}

/* Enhanced Card Styles */
.card {
    background: rgba(26, 26, 26, 0.9) !important;
    border: 3px solid rgb(255, 255, 255) !important;
    /* border-top: 3px solid rgb(255, 255, 255) !important; */
    color: #ffffff !important;
    margin:0.5rem;
    backdrop-filter: blur(10px);
    border-radius: 15px !important;
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    border-color: rgba(255, 255, 255, 0.973) !important;
    box-shadow: 0 8px 32px rgb(255, 255, 255);
}

.card-header {
    background: rgba(42, 42, 42, 0.8) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;
    font-weight: 600;
}

.card-body {
    background: rgba(26, 26, 26, 0.9) !important;
    color: #ffffff !important;
    padding: 1.5rem !important;
}

/* PG Listing Card Styles */
.listing-card {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    background: rgba(26, 26, 26, 0.95) !important;
    backdrop-filter: blur(15px);
    border-radius: 20px !important;
    overflow: hidden;
    margin-bottom: 2.5rem !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.listing-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(248, 248, 248, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.listing-card .card-img-top {
    height: 220px;
    object-fit: cover;
    transition: transform 0.4s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.listing-card:hover .card-img-top {
    transform: scale(1.05);
    cursor:pointer;
}

.listing-card .card-title {
    color: #ffffff !important;
    font-weight: 600;
    font-size: 1.3rem;
    margin-bottom: 1rem !important;
}

.listing-card .card-text {
    color: rgba(255, 255, 255, 0.8) !important;
    line-height: 1.6;
    margin-bottom: 1.5rem !important;
}

/* Enhanced Price Badge */
.price-badge {
    background: linear-gradient(135deg, #28a745, #20c997, #17a2b8) !important;
    color: white;
    font-weight: 700;
    padding: 0.6rem 1.2rem;
    border-radius: 25px;
    font-size: 1rem;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Owner Info Styling */
.owner-info {
    background: rgba(13, 110, 253, 0.1);
    border: 1px solid rgba(13, 110, 253, 0.2);
    border-radius: 10px;
    padding: 0.8rem;
    margin-bottom: 1rem;
}

.owner-info small {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
}

/* Location and Landmark Info */
.location-info, .landmark-info {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 0.6rem;
    margin-bottom: 0.8rem;
}

.location-info small, .landmark-info small {
    color: rgba(255, 255, 255, 0.85) !important;
}

/* Detail Page Styles */
.listing-detail-card {
    border: none;
    background: rgba(26, 26, 26, 0.95) !important;
    backdrop-filter: blur(15px);
    border-radius: 25px !important;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.listing-image {
    height: 450px;
    object-fit: cover;
    width: 100%;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.price-display {
    background: linear-gradient(135deg, #28a745, #20c997, #17a2b8) !important;
    color: white;
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
    font-size: 1.8rem;
    font-weight: 700;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.detail-item {
    background: rgba(42, 42, 42, 0.8) !important;
    padding: 1.5rem;
    border-radius: 15px;
    margin-bottom: 1.5rem;
    border-left: 4px solid #007bff;
    color: #ffffff !important;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-icon {
    color: #007bff;
    width: 24px;
    font-size: 1.2rem;
}

/* Form Styles */
.form-control {
    background: rgba(42, 42, 42, 0.9) !important;
    border: 2px solid rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;
    border-radius: 12px !important;
    padding: 0.8rem 1rem !important;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    background: rgba(42, 42, 42, 0.95) !important;
    border-color: #007bff !important;
    color: #ffffff !important;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25) !important;
    transform: translateY(-1px);
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.6) !important;
}

.form-label {
    color: #ffffff !important;
    font-weight: 600;
    margin-bottom: 0.8rem;
    font-size: 1rem;
}

.input-group-text {
    background: rgba(42, 42, 42, 0.9) !important;
    border: 2px solid rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;
    border-radius: 12px 0 0 12px !important;
    font-weight: 600;
}

/* Enhanced Button Styles */
.btn {
    border-radius: 12px !important;
    padding: 0.8rem 1.5rem !important;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none !important;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085) !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #1e7e34) !important;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, #1e7e34, #155724) !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333) !important;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c82333, #a71e2a) !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

.btn-outline-primary {
    color: #007bff !important;
    border: 2px solid #007bff !important;
    background: rgba(0, 123, 255, 0.1) !important;
}

.btn-outline-primary:hover {
    background: #007bff !important;
    border-color: #007bff !important;
    color: #ffffff !important;
    transform: translateY(-2px);
}

.btn-outline-secondary {
    color: rgba(255, 255, 255, 0.8) !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    background: rgba(255, 255, 255, 0.05) !important;
}

.btn-outline-secondary:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    color: #ffffff !important;
    transform: translateY(-2px);
}

/* Footer Styles */
footer {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(15px);
    color: white;
    margin-top: auto;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.5);
    margin-left: 320px;
    transition: margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

footer.sidebar-hidden {
    margin-left: 0;
}

.footer-link {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
    padding: 0.2rem 0;
    display: inline-block;
}

.footer-link:hover {
    color: #007bff;
    text-decoration: none;
    transform: translateX(5px);
}

.social-icon {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.4rem;
    margin: 0 12px;
    transition: all 0.3s ease;
    padding: 0.5rem;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.05);
}

.social-icon:hover {
    color: #007bff;
    background: rgba(0, 123, 255, 0.1);
    transform: translateY(-3px);
}

/* Breadcrumb Styles */
.breadcrumb {
    background: rgba(42, 42, 42, 0.8) !important;
    border-radius: 12px;
    padding: 1rem 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.breadcrumb-item a {
    color: #007bff !important;
    text-decoration: none;
    transition: all 0.3s ease;
}

.breadcrumb-item a:hover {
    color: #0056b3 !important;
}

.breadcrumb-item.active {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Alert Styles */
.alert-info {
    background: rgba(13, 110, 253, 0.1) !important;
    border: 1px solid rgba(13, 110, 253, 0.3) !important;
    color: #ffffff !important;
    border-radius: 12px;
    backdrop-filter: blur(10px);
}

.alert-info a {
    color: #007bff !important;
    font-weight: 600;
}

/* Text Styles */
.text-muted {
    color: rgba(255, 255, 255, 0.7) !important;
}

.text-danger {
    color: #dc3545 !important;
}

/* List Styles */
.list-unstyled li {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0.5rem;
}

/* Small Text */
small.text-muted {
    color: rgba(255, 255, 255, 0.7) !important;
}

/* HR Styles */
hr {
    border-color: rgba(255, 255, 255, 0.2) !important;
    margin: 2rem 0;
}

/* Container Styles */
.container {
    color: #ffffff;
}

/* Badge Styles */
.badge {
    color: #ffffff;
    font-weight: 600;
}

/* Image Styles */
.card-img-top {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Page Header Styles */
.page-header {
    background: rgba(42, 42, 42, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.page-header h2 {
    color: #ffffff;
    font-weight: 700;
    margin-bottom: 0;
}

/* Search Container Styles */
.search-container {
    max-width: 600px;
    margin: 0 auto;
}

.search-form .input-group {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    overflow: hidden;
}

.search-input {
    border: none !important;
    background: rgba(255, 255, 255, 0.95) !important;
    color: #333 !important;
    font-size: 1.1rem;
    padding: 1rem 1.5rem !important;
}

.search-input:focus {
    background: rgba(255, 255, 255, 1) !important;
    color: #333 !important;
    box-shadow: none !important;
}

.search-input::placeholder {
    color: rgba(0, 0, 0, 0.6) !important;
}

.search-btn {
    border: none !important;
    padding: 1rem 1.5rem !important;
    font-weight: 600;
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
}

.search-btn:hover {
    background: linear-gradient(135deg, #0056b3, #004085) !important;
    transform: none;
}

.search-results-info {
    text-align: center;
    padding: 1rem;
    background: rgba(13, 110, 253, 0.1);
    border-radius: 10px;
    border: 1px solid rgba(13, 110, 253, 0.2);
}

.search-results-info p {
    color: #ffffff !important;
    font-size: 1rem;
}

/* Listings Container */
.listings-container {
    padding: 2rem 0;
}

.listing-row {
    margin-bottom: 1rem;
}

/* Enhanced Spacing */
.card-body .card-title {
    margin-bottom: 1.2rem !important;
}

.card-body .card-text {
    margin-bottom: 1.5rem !important;
    line-height: 1.6;
}

/* PG Image Slider Styles */
.pg-slider-container {
    margin-bottom: 2rem;
    /* border:2px solid white; */
    width: 100%;
    max-width: 1200px;
    margin: 0 auto 2rem auto;
    padding: 0 20px;
    box-sizing: border-box;
}

.pg-slider {
    position: relative;
    width: 100%;
    height: 500px;
    border-radius: 15px;
    /* border:2px solid white; */
    overflow: hidden;
    background: #000000;
    box-shadow: 0 15px 35px rgba(255, 255, 255, 0.3),
                0 5px 15px rgba(255, 255, 255, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.1);
    border: 3px solid rgba(255, 255, 255, 0.2);
    cursor: grab;
}

.pg-slider:active {
    cursor: grabbing;
}

.slider-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.5s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
}

.slide.active {
    opacity: 1;
    transform: translateX(0);
}

.slide.slide-out-left {
    opacity: 1;
    transform: translateX(-100%);
}

.slide.slide-out-right {
    opacity: 1;
    transform: translateX(100%);
}

.slide.slide-in-left {
    opacity: 0;
    transform: translateX(-100%);
    animation: slideInLeft 0.5s ease-in-out forwards;
}

.slide.slide-in-right {
    opacity: 0;
    transform: translateX(100%);
    animation: slideInRight 0.5s ease-in-out forwards;
}

.slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    user-select: none;
    pointer-events: none;
    display: block;
    border: none;
    outline: none;
}

/* Slide animations */
@keyframes slideInLeft {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Navigation Buttons - Hidden */
.slider-btn {
    display: none;
}

/* Slide Indicators */
.slider-indicators {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 10;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.4);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.indicator.active {
    background: #007bff;
    border-color: rgba(255, 255, 255, 0.6);
    transform: scale(1.2);
}

.indicator:hover {
    background: rgba(255, 255, 255, 0.7);
    transform: scale(1.1);
}

/* Bootstrap Validation Feedback Styles */
.valid-feedback {
    color: #28a745 !important;
    font-size: 0.875rem;
    font-weight: 500;
    margin-top: 0.5rem;
}

.invalid-feedback {
    color: #dc3545 !important;
    font-size: 0.875rem;
    font-weight: 500;
    margin-top: 0.5rem;
}

.form-control.is-valid {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    background-color: rgba(40, 167, 69, 0.1) !important;
}

.form-control.is-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    background-color: rgba(220, 53, 69, 0.1) !important;
}

.input-group .form-control.is-valid {
    border-color: #28a745 !important;
}

.input-group .form-control.is-invalid {
    border-color: #dc3545 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    /* Hide sidebar by default on mobile */
    .sidebar {
        left: -320px;
    }

    .sidebar.hidden {
        left: -320px;
    }

    main {
        margin-left: 0;
    }

    main.sidebar-hidden {
        margin-left: 0;
    }

    footer {
        margin-left: 0;
    }

    footer.sidebar-hidden {
        margin-left: 0;
    }
    .listing-image {
        height: 280px;
    }

    .price-display {
        font-size: 1.4rem;
        padding: 1.2rem;
    }

    .navbar-brand {
        font-size: 1.4rem;
    }

    .listing-card {
        margin-bottom: 2.5rem !important;
    }

    .card-body {
        padding: 1.25rem !important;
    }

    .detail-item {
        padding: 1.2rem;
    }

    .btn {
        padding: 0.7rem 1.2rem !important;
    }

    /* Slider responsive styles */
    .pg-slider-container {
        max-width: 900px;
        padding: 0 15px;
    }

    .pg-slider {
        height: 380px;
        border-width: 2px;
        border-radius: 12px;
    }

    .indicator {
        width: 10px;
        height: 10px;
    }
}

@media (max-width: 576px) {
    .listing-card .card-img-top {
        height: 200px;
    }

    .price-display {
        font-size: 1.2rem;
        padding: 1rem;
    }

    .social-icon {
        font-size: 1.2rem;
        margin: 0 8px;
    }

    /* Slider mobile styles */
    .pg-slider-container {
        max-width: 100%;
        padding: 0 10px;
    }

    .pg-slider {
        height: 300px;
        border-width: 2px;
        border-radius: 10px;
    }

    .slider-indicators {
        bottom: 15px;
        gap: 8px;
    }

    .indicator {
        width: 8px;
        height: 8px;
    }
}
